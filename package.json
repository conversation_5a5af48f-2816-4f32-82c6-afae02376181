{"name": "user_management", "version": "1.0.0", "description": "", "main": "index.js", "private": true, "scripts": {"dev": "turbo run dev", "build": "turbo run build", "lint": "turbo run lint", "typecheck": "turbo run typecheck", "format": "turbo run format", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"@prisma/client": "^6.13.0", "prisma": "^6.13.0", "zod": "^4.0.14"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "3.6.2", "turbo": "^2.5.5", "typescript": "^5.8.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}