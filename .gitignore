# ---------------------------------------------------
# Node.js
# ---------------------------------------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
yarn.lock
.pnpm-debug.log

# ---------------------------------------------------
# Build output
# ---------------------------------------------------
dist/
build/
.out/
.next/
.next.cache/
*.tsbuildinfo

# ---------------------------------------------------
# Logs
# ---------------------------------------------------
logs
*.log
*.log.*
lerna-debug.log

# ---------------------------------------------------
# Environment files
# (keep .env.example for reference if you want)
# ---------------------------------------------------
.env
.env.local
.env.*.local

# ---------------------------------------------------
# OS / Editor
# ---------------------------------------------------
.DS_Store
Thumbs.db
*.swp
*.swo
*.vim
.idea/
.vscode/
*.code-workspace

# ---------------------------------------------------
# Testing
# ---------------------------------------------------
coverage/
.nyc_output/
jest-html-reporters-*
