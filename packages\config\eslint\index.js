/** ESLint shared configuration */
module.exports = {
  root: false,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react', 'prettier'],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:prettier/recommended'
  ],
  env: { browser: true, node: true, es2021: true },
  settings: {
    react: { version: 'detect' }
  },
  rules: {
    'prettier/prettier': 'error'
  }
};
